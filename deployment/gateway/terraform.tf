# MCP Rules Engine - Complete Infrastructure as Code
# This Terraform configuration deploys the entire MCP Rules Engine infrastructure

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
  
  # Uncomment and configure for remote state
  # backend "gcs" {
  #   bucket = "ailex-rules-terraform-state"
  #   prefix = "mcp-rules-engine"
  # }
}

# Variables
variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
  default     = "ailex-rules-prod"
}

variable "region" {
  description = "Google Cloud Region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment (staging or production)"
  type        = string
  default     = "production"
  
  validation {
    condition     = contains(["staging", "production"], var.environment)
    error_message = "Environment must be either 'staging' or 'production'."
  }
}

variable "domain_name" {
  description = "Custom domain name for the API"
  type        = string
  default     = "rules.ailexlaw.com"
}

variable "staging_domain_name" {
  description = "Custom domain name for staging API"
  type        = string
  default     = "staging-rules.ailexlaw.com"
}

# Local values
locals {
  service_name = var.environment == "production" ? "mcp-prod" : "mcp-staging"
  gateway_name = "mcp-rules-gateway-${var.environment}"
  config_name  = "mcp-rules-config-${var.environment}"
  domain       = var.environment == "production" ? var.domain_name : var.staging_domain_name
}

# Provider configuration
provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "cloudbuild.googleapis.com",
    "run.googleapis.com",
    "apigateway.googleapis.com",
    "servicemanagement.googleapis.com",
    "servicecontrol.googleapis.com",
    "secretmanager.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com"
  ])
  
  service = each.value
  
  disable_dependent_services = false
  disable_on_destroy        = false
}

# Secret Manager for API keys and sensitive data
resource "google_secret_manager_secret" "gemini_api_key" {
  secret_id = "gemini-api-key"
  
  replication {
    auto {}
  }
  
  depends_on = [google_project_service.required_apis]
}

# Cloud Run Service
resource "google_cloud_run_v2_service" "mcp_service" {
  name     = local.service_name
  location = var.region
  
  template {
    scaling {
      min_instance_count = var.environment == "production" ? 1 : 0
      max_instance_count = var.environment == "production" ? 20 : 10
    }
    
    containers {
      image = "gcr.io/${var.project_id}/mcp-rules-engine:latest"
      
      ports {
        container_port = 4000
      }
      
      env {
        name  = "NODE_ENV"
        value = var.environment
      }
      
      env {
        name  = "PORT"
        value = "4000"
      }
      
      env {
        name = "GEMINI_API_KEY"
        value_source {
          secret_key_ref {
            secret  = google_secret_manager_secret.gemini_api_key.secret_id
            version = "latest"
          }
        }
      }
      
      resources {
        limits = {
          cpu    = "1"
          memory = "1Gi"
        }
      }
      
      startup_probe {
        http_get {
          path = "/health"
          port = 4000
        }
        initial_delay_seconds = 30
        timeout_seconds       = 5
        period_seconds        = 10
        failure_threshold     = 3
      }
      
      liveness_probe {
        http_get {
          path = "/health"
          port = 4000
        }
        initial_delay_seconds = 30
        timeout_seconds       = 5
        period_seconds        = 10
        failure_threshold     = 3
      }
    }
    
    timeout = "300s"
    
    service_account = google_service_account.cloud_run_sa.email
  }
  
  traffic {
    percent = 100
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
  }
  
  depends_on = [google_project_service.required_apis]
}

# Service Account for Cloud Run
resource "google_service_account" "cloud_run_sa" {
  account_id   = "mcp-cloud-run-${var.environment}"
  display_name = "MCP Cloud Run Service Account (${var.environment})"
}

# IAM binding for Secret Manager access
resource "google_secret_manager_secret_iam_member" "cloud_run_secret_access" {
  secret_id = google_secret_manager_secret.gemini_api_key.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

# Cloud Run IAM - Allow unauthenticated access (API Gateway will handle auth)
resource "google_cloud_run_service_iam_member" "public_access" {
  service  = google_cloud_run_v2_service.mcp_service.name
  location = google_cloud_run_v2_service.mcp_service.location
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# API Gateway API
resource "google_api_gateway_api" "mcp_api" {
  provider = google
  api_id   = "mcp-rules-gateway"
  
  depends_on = [google_project_service.required_apis]
}

# API Gateway Config
resource "google_api_gateway_api_config" "mcp_config" {
  provider      = google
  api           = google_api_gateway_api.mcp_api.api_id
  api_config_id = local.config_name
  
  openapi_documents {
    document {
      path = "openapi.yaml"
      contents = base64encode(templatefile("${path.module}/openapi.yaml", {
        cloud_run_url = google_cloud_run_v2_service.mcp_service.uri
        domain_name   = local.domain
      }))
    }
  }
  
  lifecycle {
    create_before_destroy = true
  }
  
  depends_on = [google_project_service.required_apis]
}

# API Gateway
resource "google_api_gateway_gateway" "mcp_gateway" {
  provider   = google
  api_config = google_api_gateway_api_config.mcp_config.id
  gateway_id = local.gateway_name
  region     = var.region
  
  depends_on = [google_project_service.required_apis]
}

# Monitoring - Uptime Check
resource "google_monitoring_uptime_check_config" "mcp_uptime_check" {
  display_name = "MCP Rules Engine Uptime Check (${var.environment})"
  timeout      = "10s"
  period       = "300s"
  
  http_check {
    path         = "/health"
    port         = "443"
    use_ssl      = true
    validate_ssl = true
  }
  
  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = google_api_gateway_gateway.mcp_gateway.default_hostname
    }
  }
  
  content_matchers {
    content = "healthy"
    matcher = "CONTAINS_STRING"
  }
}

# Outputs
output "cloud_run_url" {
  description = "Cloud Run service URL"
  value       = google_cloud_run_v2_service.mcp_service.uri
}

output "api_gateway_url" {
  description = "API Gateway URL"
  value       = "https://${google_api_gateway_gateway.mcp_gateway.default_hostname}"
}

output "custom_domain" {
  description = "Custom domain (configure DNS separately)"
  value       = "https://${local.domain}"
}

output "service_account_email" {
  description = "Cloud Run service account email"
  value       = google_service_account.cloud_run_sa.email
}

output "gateway_id" {
  description = "API Gateway ID"
  value       = google_api_gateway_gateway.mcp_gateway.gateway_id
}
