#!/bin/bash

# MCP Rules Engine - Monitoring Setup Script
# Sets up comprehensive monitoring, alerting, and logging

set -e

# Configuration
PROJECT_ID="ailex-rules-prod"
REGION="us-central1"
NOTIFICATION_EMAIL="<EMAIL>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[MONITOR]${NC} $1"
}

print_header "🔍 Setting up monitoring for MCP Rules Engine"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
print_status "Enabling monitoring APIs..."
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable clouderrorreporting.googleapis.com

# Create notification channel for email alerts
print_status "Creating notification channel..."
NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels create \
    --display-name="MCP Rules Engine Alerts" \
    --type=email \
    --channel-labels=email_address=$NOTIFICATION_EMAIL \
    --format="value(name)" 2>/dev/null || echo "")

if [[ -z "$NOTIFICATION_CHANNEL" ]]; then
    print_warning "Failed to create notification channel or it already exists"
    # Try to find existing channel
    NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels list \
        --filter="displayName:'MCP Rules Engine Alerts'" \
        --format="value(name)" | head -1)
fi

if [[ -n "$NOTIFICATION_CHANNEL" ]]; then
    print_status "Using notification channel: $NOTIFICATION_CHANNEL"
else
    print_warning "No notification channel available - alerts will be created without notifications"
fi

# Create alerting policies
print_status "Creating alerting policies..."

# 1. High Error Rate Alert
cat > /tmp/high-error-rate-policy.yaml << EOF
displayName: "MCP Rules Engine - High Error Rate"
documentation:
  content: "Error rate is above 5% for the MCP Rules Engine API Gateway"
  mimeType: "text/markdown"
conditions:
  - displayName: "Error rate above 5%"
    conditionThreshold:
      filter: 'resource.type="api" AND resource.labels.service="mcp-rules-gateway"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.05
      duration: 300s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_RATE
          crossSeriesReducer: REDUCE_MEAN
          groupByFields:
            - "resource.label.service"
combiner: OR
enabled: true
EOF

if [[ -n "$NOTIFICATION_CHANNEL" ]]; then
    echo "notificationChannels:" >> /tmp/high-error-rate-policy.yaml
    echo "  - $NOTIFICATION_CHANNEL" >> /tmp/high-error-rate-policy.yaml
fi

gcloud alpha monitoring policies create --policy-from-file=/tmp/high-error-rate-policy.yaml

# 2. High Latency Alert
cat > /tmp/high-latency-policy.yaml << EOF
displayName: "MCP Rules Engine - High Latency"
documentation:
  content: "99th percentile latency is above 2 seconds for the MCP Rules Engine"
  mimeType: "text/markdown"
conditions:
  - displayName: "Latency P99 above 2s"
    conditionThreshold:
      filter: 'resource.type="api" AND resource.labels.service="mcp-rules-gateway"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 2000
      duration: 300s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_DELTA
          crossSeriesReducer: REDUCE_PERCENTILE_99
          groupByFields:
            - "resource.label.service"
combiner: OR
enabled: true
EOF

if [[ -n "$NOTIFICATION_CHANNEL" ]]; then
    echo "notificationChannels:" >> /tmp/high-latency-policy.yaml
    echo "  - $NOTIFICATION_CHANNEL" >> /tmp/high-latency-policy.yaml
fi

gcloud alpha monitoring policies create --policy-from-file=/tmp/high-latency-policy.yaml

# 3. Cloud Run Instance Down Alert
cat > /tmp/cloud-run-down-policy.yaml << EOF
displayName: "MCP Rules Engine - Cloud Run Service Down"
documentation:
  content: "Cloud Run service is not responding to health checks"
  mimeType: "text/markdown"
conditions:
  - displayName: "Cloud Run service down"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name=~"mcp-(prod|staging)"'
      comparison: COMPARISON_LESS_THAN
      thresholdValue: 1
      duration: 180s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_SUM
          groupByFields:
            - "resource.label.service_name"
combiner: OR
enabled: true
EOF

if [[ -n "$NOTIFICATION_CHANNEL" ]]; then
    echo "notificationChannels:" >> /tmp/cloud-run-down-policy.yaml
    echo "  - $NOTIFICATION_CHANNEL" >> /tmp/cloud-run-down-policy.yaml
fi

gcloud alpha monitoring policies create --policy-from-file=/tmp/cloud-run-down-policy.yaml

# 4. API Gateway Quota Exceeded Alert
cat > /tmp/quota-exceeded-policy.yaml << EOF
displayName: "MCP Rules Engine - API Quota Exceeded"
documentation:
  content: "API Gateway quota usage is above 90%"
  mimeType: "text/markdown"
conditions:
  - displayName: "Quota usage above 90%"
    conditionThreshold:
      filter: 'resource.type="api" AND metric.type="serviceruntime.googleapis.com/quota/used"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.9
      duration: 300s
      aggregations:
        - alignmentPeriod: 300s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_MAX
combiner: OR
enabled: true
EOF

if [[ -n "$NOTIFICATION_CHANNEL" ]]; then
    echo "notificationChannels:" >> /tmp/quota-exceeded-policy.yaml
    echo "  - $NOTIFICATION_CHANNEL" >> /tmp/quota-exceeded-policy.yaml
fi

gcloud alpha monitoring policies create --policy-from-file=/tmp/quota-exceeded-policy.yaml

# Create custom dashboard
print_status "Creating monitoring dashboard..."
cat > /tmp/mcp-dashboard.json << EOF
{
  "displayName": "MCP Rules Engine Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "API Gateway Request Rate",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_RATE",
                      "crossSeriesReducer": "REDUCE_SUM"
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Requests/sec",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "xPos": 6,
        "widget": {
          "title": "API Gateway Error Rate",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\" AND metric.labels.response_code_class=\"5xx\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_RATE",
                      "crossSeriesReducer": "REDUCE_SUM"
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Errors/sec",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 12,
        "height": 4,
        "yPos": 4,
        "widget": {
          "title": "Cloud Run CPU and Memory Usage",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"mcp-(prod|staging)\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_MEAN"
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Usage %",
              "scale": "LINEAR"
            }
          }
        }
      }
    ]
  }
}
EOF

gcloud monitoring dashboards create --config-from-file=/tmp/mcp-dashboard.json

# Set up log-based metrics
print_status "Creating log-based metrics..."

# API Gateway request count by status code
gcloud logging metrics create api_gateway_requests \
    --description="API Gateway requests by status code" \
    --log-filter='resource.type="api_gateway" AND httpRequest.status>=200' \
    --value-extractor='EXTRACT(httpRequest.status)'

# Cloud Run error count
gcloud logging metrics create cloud_run_errors \
    --description="Cloud Run application errors" \
    --log-filter='resource.type="cloud_run_revision" AND severity>=ERROR'

# Cleanup temporary files
rm -f /tmp/*-policy.yaml /tmp/mcp-dashboard.json

print_status "✅ Monitoring setup completed!"
echo ""
print_header "📊 MONITORING SUMMARY"
echo "✅ Alerting policies created:"
echo "   • High error rate (>5%)"
echo "   • High latency (P99 >2s)"
echo "   • Cloud Run service down"
echo "   • API quota exceeded (>90%)"
echo ""
echo "✅ Custom dashboard created"
echo "✅ Log-based metrics configured"
echo "✅ Notification channel: $NOTIFICATION_EMAIL"
echo ""
print_header "🔗 USEFUL LINKS"
echo "• Monitoring Console: https://console.cloud.google.com/monitoring"
echo "• Logs Explorer: https://console.cloud.google.com/logs"
echo "• Error Reporting: https://console.cloud.google.com/errors"
echo ""
print_status "🎉 Monitoring setup completed successfully!"
