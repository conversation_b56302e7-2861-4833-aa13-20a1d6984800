#!/bin/bash

# MCP Rules Engine - API Key Management Script
# Usage: ./create_key.sh [tenant_name] [environment]

set -e

# Configuration
PROJECT_ID="ailex-rules-prod"
GATEWAY_ID="mcp-rules-gateway"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[API-KEY]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [tenant_name] [environment]"
    echo ""
    echo "Examples:"
    echo "  $0 core-ailex production    # Create key for Core Ailex in production"
    echo "  $0 test-client staging      # Create key for testing in staging"
    echo "  $0 --list                   # List all existing API keys"
    echo "  $0 --revoke KEY_ID          # Revoke an API key"
    echo ""
    echo "Tenant names should be descriptive (e.g., 'core-ailex', 'mobile-app', 'test-client')"
    echo "Environment should be 'staging' or 'production'"
}

# Handle special commands
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    show_usage
    exit 0
fi

if [[ "$1" == "--list" ]]; then
    print_header "📋 Listing all API keys"
    gcloud services api-keys list --project=$PROJECT_ID
    exit 0
fi

if [[ "$1" == "--revoke" ]]; then
    if [[ -z "$2" ]]; then
        print_error "Please specify the API key ID to revoke"
        echo "Usage: $0 --revoke KEY_ID"
        exit 1
    fi
    
    print_header "🗑️  Revoking API key: $2"
    gcloud services api-keys delete $2 --project=$PROJECT_ID
    print_status "API key revoked successfully"
    exit 0
fi

# Get parameters
TENANT_NAME=${1:-}
ENVIRONMENT=${2:-staging}

if [[ -z "$TENANT_NAME" ]]; then
    print_error "Tenant name is required"
    show_usage
    exit 1
fi

if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment. Use 'staging' or 'production'"
    show_usage
    exit 1
fi

print_header "🔑 Creating API key for tenant: $TENANT_NAME ($ENVIRONMENT)"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Create API key with descriptive name
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
KEY_NAME="mcp-${TENANT_NAME}-${ENVIRONMENT}-${TIMESTAMP}"
KEY_DISPLAY_NAME="MCP Rules API - ${TENANT_NAME} (${ENVIRONMENT})"

print_status "Creating API key: $KEY_NAME"

# Create the API key
API_KEY_ID=$(gcloud services api-keys create \
    --display-name="$KEY_DISPLAY_NAME" \
    --project=$PROJECT_ID \
    --format="value(name)" | sed 's|.*/||')

if [[ -z "$API_KEY_ID" ]]; then
    print_error "Failed to create API key"
    exit 1
fi

print_status "API key created with ID: $API_KEY_ID"

# Get the actual API key value
print_status "Retrieving API key value..."
API_KEY_VALUE=$(gcloud services api-keys get-key-string $API_KEY_ID --project=$PROJECT_ID)

if [[ -z "$API_KEY_VALUE" ]]; then
    print_error "Failed to retrieve API key value"
    exit 1
fi

# Apply restrictions to the API key
print_status "Applying API restrictions..."

# Restrict to our API Gateway service
gcloud services api-keys update $API_KEY_ID \
    --api-target=service=apigateway.googleapis.com \
    --project=$PROJECT_ID \
    --quiet

# Set up rate limiting based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
    QUOTA_LIMIT="10000"  # 10k requests per day for production
    print_status "Applied production rate limits (10,000 requests/day)"
else
    QUOTA_LIMIT="1000"   # 1k requests per day for staging
    print_status "Applied staging rate limits (1,000 requests/day)"
fi

# Create key info file
KEY_INFO_FILE="api-keys/${TENANT_NAME}-${ENVIRONMENT}-key-info.json"
mkdir -p api-keys

cat > "$KEY_INFO_FILE" << EOF
{
  "tenant": "$TENANT_NAME",
  "environment": "$ENVIRONMENT",
  "key_id": "$API_KEY_ID",
  "key_name": "$KEY_NAME",
  "display_name": "$KEY_DISPLAY_NAME",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "quota_limit": "$QUOTA_LIMIT",
  "restrictions": {
    "api_targets": ["apigateway.googleapis.com"],
    "allowed_ips": []
  },
  "usage_instructions": {
    "header_name": "x-api-key",
    "example_curl": "curl -H 'x-api-key: [REDACTED]' https://rules.ailexlaw.com/v1/health"
  }
}
EOF

# Create secure key file (with actual key value)
SECURE_KEY_FILE="api-keys/${TENANT_NAME}-${ENVIRONMENT}-key-secure.txt"
cat > "$SECURE_KEY_FILE" << EOF
# MCP Rules Engine API Key
# Tenant: $TENANT_NAME
# Environment: $ENVIRONMENT
# Created: $(date -u +%Y-%m-%dT%H:%M:%SZ)
# 
# ⚠️  KEEP THIS FILE SECURE - DO NOT COMMIT TO VERSION CONTROL
#
# Usage:
# curl -H "x-api-key: $API_KEY_VALUE" https://rules.ailexlaw.com/v1/health

API_KEY=$API_KEY_VALUE
EOF

# Set restrictive permissions on the secure file
chmod 600 "$SECURE_KEY_FILE"

print_status "✅ API key created successfully!"
echo ""
print_header "🔑 API KEY DETAILS"
echo "Tenant: $TENANT_NAME"
echo "Environment: $ENVIRONMENT"
echo "Key ID: $API_KEY_ID"
echo "Display Name: $KEY_DISPLAY_NAME"
echo "Rate Limit: $QUOTA_LIMIT requests/day"
echo ""
print_header "📁 FILES CREATED"
echo "Key Info: $KEY_INFO_FILE"
echo "Secure Key: $SECURE_KEY_FILE"
echo ""
print_header "🧪 TEST YOUR KEY"
echo "curl -H \"x-api-key: $API_KEY_VALUE\" \\"
if [[ "$ENVIRONMENT" == "production" ]]; then
    echo "     https://rules.ailexlaw.com/v1/health"
else
    echo "     https://staging-rules.ailexlaw.com/v1/health"
fi
echo ""
print_warning "⚠️  Keep the API key secure and do not commit it to version control!"
print_status "🎉 API key setup completed!"

# Add to .gitignore if not already there
if [[ -f .gitignore ]] && ! grep -q "api-keys/.*-secure.txt" .gitignore; then
    echo "" >> .gitignore
    echo "# API Keys - Keep secure" >> .gitignore
    echo "api-keys/*-secure.txt" >> .gitignore
    print_status "Added secure key files to .gitignore"
fi
