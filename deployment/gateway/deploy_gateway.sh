#!/bin/bash

# MCP Rules Engine - API Gateway Deployment Script
# Usage: ./deploy_gateway.sh [staging|production]

set -e

# Configuration
PROJECT_ID="ailex-rules-prod"
REGION="us-central1"
GATEWAY_ID="mcp-rules-gateway"
CONFIG_ID="mcp-rules-config"
STAGING_DOMAIN="staging-rules.ailexlaw.com"
PRODUCTION_DOMAIN="rules.ailexlaw.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Check if environment is specified
ENVIRONMENT=${1:-staging}
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment. Use 'staging' or 'production'"
    echo "Usage: $0 [staging|production]"
    exit 1
fi

print_header "🚀 Deploying API Gateway for $ENVIRONMENT environment"

# Set environment-specific variables
if [[ "$ENVIRONMENT" == "production" ]]; then
    DOMAIN=$PRODUCTION_DOMAIN
    CLOUD_RUN_SERVICE="mcp-prod"
    ENV_SUFFIX="prod"
else
    DOMAIN=$STAGING_DOMAIN
    CLOUD_RUN_SERVICE="mcp-staging"
    ENV_SUFFIX="staging"
fi

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
print_status "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
print_status "Enabling required APIs..."
gcloud services enable apigateway.googleapis.com
gcloud services enable servicemanagement.googleapis.com
gcloud services enable servicecontrol.googleapis.com

# Get Cloud Run service URL
print_status "Getting Cloud Run service URL..."
CLOUD_RUN_URL=$(gcloud run services describe $CLOUD_RUN_SERVICE \
    --region=$REGION \
    --format="value(status.url)" 2>/dev/null || echo "")

if [[ -z "$CLOUD_RUN_URL" ]]; then
    print_error "Cloud Run service '$CLOUD_RUN_SERVICE' not found in region '$REGION'"
    print_error "Please deploy the Cloud Run service first using: ./cloud-run-deploy.sh $ENVIRONMENT"
    exit 1
fi

print_status "Found Cloud Run service: $CLOUD_RUN_URL"

# Update OpenAPI spec with actual Cloud Run URL
print_status "Updating OpenAPI specification..."
TEMP_OPENAPI="/tmp/openapi-${ENV_SUFFIX}.yaml"
cp openapi.yaml $TEMP_OPENAPI

# Replace placeholder URLs with actual Cloud Run URL
sed -i.bak "s|https://mcp-prod-HASH-uc.a.run.app|$CLOUD_RUN_URL|g" $TEMP_OPENAPI
sed -i.bak "s|rules.ailexlaw.com|$DOMAIN|g" $TEMP_OPENAPI

# Create API config
print_status "Creating API Gateway configuration..."
CONFIG_NAME="${CONFIG_ID}-${ENV_SUFFIX}"

gcloud api-gateway api-configs create $CONFIG_NAME \
    --api=$GATEWAY_ID \
    --openapi-spec=$TEMP_OPENAPI \
    --project=$PROJECT_ID \
    --quiet || {
    print_warning "Config already exists, updating..."
    # If config exists, we need to create a new version
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    NEW_CONFIG_NAME="${CONFIG_ID}-${ENV_SUFFIX}-${TIMESTAMP}"
    gcloud api-gateway api-configs create $NEW_CONFIG_NAME \
        --api=$GATEWAY_ID \
        --openapi-spec=$TEMP_OPENAPI \
        --project=$PROJECT_ID \
        --quiet
    CONFIG_NAME=$NEW_CONFIG_NAME
}

# Create or update API Gateway
print_status "Creating/updating API Gateway..."
GATEWAY_NAME="${GATEWAY_ID}-${ENV_SUFFIX}"

gcloud api-gateway gateways create $GATEWAY_NAME \
    --api=$GATEWAY_ID \
    --api-config=$CONFIG_NAME \
    --location=$REGION \
    --project=$PROJECT_ID \
    --quiet || {
    print_warning "Gateway already exists, updating..."
    gcloud api-gateway gateways update $GATEWAY_NAME \
        --api=$GATEWAY_ID \
        --api-config=$CONFIG_NAME \
        --location=$REGION \
        --project=$PROJECT_ID \
        --quiet
}

# Get gateway URL
print_status "Getting gateway URL..."
GATEWAY_URL=$(gcloud api-gateway gateways describe $GATEWAY_NAME \
    --location=$REGION \
    --project=$PROJECT_ID \
    --format="value(defaultHostname)")

if [[ -n "$GATEWAY_URL" ]]; then
    GATEWAY_URL="https://$GATEWAY_URL"
    print_status "✅ API Gateway deployed successfully!"
    echo ""
    print_header "🔗 DEPLOYMENT DETAILS"
    echo "Environment: $ENVIRONMENT"
    echo "Gateway URL: $GATEWAY_URL"
    echo "Custom Domain: https://$DOMAIN (if configured)"
    echo "Cloud Run Backend: $CLOUD_RUN_URL"
    echo ""
    
    # Test the gateway
    print_status "Testing API Gateway..."
    if curl -f -s "$GATEWAY_URL/health" > /dev/null; then
        print_status "✅ Health check passed!"
    else
        print_warning "⚠️  Health check failed. Gateway may still be initializing..."
    fi
    
    echo ""
    print_header "📋 NEXT STEPS"
    echo "1. Create API keys: ./create_key.sh"
    echo "2. Configure custom domain mapping (if needed)"
    echo "3. Set up monitoring: ./setup_monitoring.sh"
    echo "4. Test with API key: curl -H 'x-api-key: YOUR_KEY' $GATEWAY_URL/health"
    
else
    print_error "Failed to get gateway URL"
    exit 1
fi

# Cleanup
rm -f $TEMP_OPENAPI $TEMP_OPENAPI.bak

print_status "🎉 Deployment completed successfully!"
